/*
 * Xgm003Async.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.async;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.inject.Inject;

import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.UserInfo;
import com.jast.gakuen.core.common.annotation.RxAsyncErrorCheck;
import com.jast.gakuen.core.common.annotation.RxAsyncExecute;
import com.jast.gakuen.core.common.annotation.RxAsyncInit;
import com.jast.gakuen.core.common.annotation.RxAsyncNumberCount;
import com.jast.gakuen.core.common.async.BaseCollectiveOutputAsync;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.dto.FileDTO;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.RxLogger;
import com.jast.gakuen.core.common.async.CollectiveCsvWriter;
import com.jast.gakuen.core.common.async.ICollectiveWriter;
import com.jast.gakuen.core.gk.util.UtilFormWriter;
import com.jast.gakuen.gk.pk.business.PkbGakNameLogic;
import com.jast.gakuen.common.database.pk.dao.PkbGakDAO;
import com.jast.gakuen.common.database.pk.dao.PkbGakHatbnDAO;
import com.jast.gakuen.common.database.pk.dao.PkbGakJushoDAO;
import com.jast.gakuen.common.database.pk.entity.PkbGakAR;
import com.jast.gakuen.common.database.pk.entity.PkbGakHatbnAR;
import com.jast.gakuen.common.database.pk.entity.PkbGakJushoAR;
import com.jast.gakuen.gk.xg.dto.Xgm003AsyncDTO;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;
import com.jast.gakuen.core.common.exception.GakuenException;

/**
 * 学生納付金通知書出力（CSV+PDF一括ダウンロード）非同期処理クラス
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class Xgm003Async extends BaseCollectiveOutputAsync {

	/**
	 * プロダクトコード
	 */
	public static final String PRD_CD = "Xg";

	/**
	 * 非同期処理ID
	 */
	public static final String ASYNC_EXEC_ID = "Xgm003Async";

	/**
	 * 処理名：CSV+PDF一括ダウンロード
	 */
	public static final String EXEC_PACKAGE_OUTPUT = "EXEC_PACKAGE_OUTPUT";

	/**
	 * ログ出力
	 */
	private static final RxLogger logger = new RxLogger(Xgm003Async.class);

	/**
	 * ログインユーザ情報
	 */
	private UserInfo userInfo;

	/**
	 * 学生納付金通知書サービス
	 */
	@Inject
	private IXgm003Service xgm003Service;

	/**
	 * コンストラクタ
	 *
	 * @throws Exception 例外
	 */
	public Xgm003Async() throws Exception {
		super();
	}

	/**
	 * 初期処理
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 */
	@RxAsyncInit(EXEC_PACKAGE_OUTPUT)
	public void init(final DbSession dbs, final Xgm003AsyncDTO condition) {
		// ログインユーザ情報を取得
		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		this.userInfo = sessionInfo.getLoginUser();
	}

	/**
	 * エラーチェック
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return エラーチェック結果
	 * @throws Exception 例外
	 */
	@RxAsyncErrorCheck(EXEC_PACKAGE_OUTPUT)
	public List<Message> errorCheck(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		List<Message> messageList = new ArrayList<>();

		// 基本的なバリデーション
		if (condition.getConditionHeader() == null) {
			messageList.add(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 36, "出力条件"));
		}

		// 納付金データの存在チェック
		if (condition.getConditionHeader() != null) {
			try {
				List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);
				if (dataList.isEmpty()) {
					messageList.add(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, "納付金"));
				}
			} catch (Exception e) {
				messageList.add(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, "納付金"));
			}
		}

		return messageList;
	}

	/**
	 * 処理件数取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 処理件数
	 * @throws Exception 例外
	 */
	@RxAsyncNumberCount(EXEC_PACKAGE_OUTPUT)
	public int getCount(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		// 処理件数は固定で2（CSV + PDF）
		return 2;
	}

	/**
	 * 業務処理：CSV+PDF一括ダウンロード
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncExecute(EXEC_PACKAGE_OUTPUT)
	public void executePackageOutput(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {

		logger.info("CSV+PDF一括ダウンロード処理開始");
		
		// 圧縮対象ファイルのリスト
		List<FileDTO> zipFileDtoList = new ArrayList<>();
		
		try {
			// 1. CSV ファイル生成
			FileDTO csvFileDto = generateCsvFile(dbs, condition);
			if (csvFileDto != null) {
				zipFileDtoList.add(csvFileDto);
				addNormalCount(1);
				addCount(1);
			}
			
			// 2. PDF ファイル生成
			FileDTO pdfFileDto = generatePdfFile(dbs, condition);
			if (pdfFileDto != null) {
				zipFileDtoList.add(pdfFileDto);
				addNormalCount(1);
				addCount(1);
			}
			
			// 3. ZIP ファイル生成
			if (!zipFileDtoList.isEmpty()) {
				FileDTO zipFileDTO = getOutputFileDTO(dbs, condition.getPrdCd(), condition.getZipFileId(), FileTypeConst.ZIP);
				// ZIPファイルを生成し、圧縮対象ファイルを登録する
				compressOutputFile(zipFileDTO, zipFileDtoList, true);
			}
			
		} catch (Exception e) {
			logger.error(Xgm003Async.class, e);
			addErrorCount(1);
			throw e;
		}

		logger.info("CSV+PDF一括ダウンロード処理終了");
	}

	/**
	 * CSV ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return CSV ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generateCsvFile(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		logger.info("CSV ファイル生成開始");

		// CSV ファイルのFileDTOを取得
		FileDTO csvFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), condition.getCsvFileId(), FileTypeConst.CSV);

		// 三菱UFJ Factor向けのCSVフォーマットでデータを出力
		try (ICollectiveWriter csvWriter = new CollectiveCsvWriter(csvFileDto)) {

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// データ行を出力（三菱UFJ Factor指定フォーマット）
			for (Xgm003DTO02 data : dataList) {
				writeCsvDataLine(csvWriter, data, condition);
				addNormalCount(1);
				addCount(1);
			}

			// CSVファイルをクローズ
			csvWriter.close();

			logger.info("CSV ファイル生成完了：" + dataList.size() + "件");
		}

		logger.info("CSV ファイル生成終了");
		return csvFileDto;
	}

	/**
	 * PDF ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return PDF ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generatePdfFile(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		logger.info("PDF ファイル生成開始");
		
		// PDF ファイルのFileDTOを取得
		FileDTO pdfFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), condition.getPdfFileId(), FileTypeConst.PDF);
		
		// SVF を使用してPDF生成
		UtilFormWriter pdfWriter = new UtilFormWriter();

		try {
			// PDF ファイルを開く
			pdfWriter.open(UtilFormWriter.DocFileType.PDF, pdfFileDto.getAbsolutePath());

			// SVF テンプレート（Xgm003RPT01.xml）を使用してPDF生成
			pdfWriter.setForm(com.jast.gakuen.core.common.constant.code.PrdKbn.GH, "Xgm003RPT01");

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// 各納付金データに対してPDFページを生成
			for (Xgm003DTO02 data : dataList) {
				// 基本情報を設定
				pdfWriter.print("年度", String.valueOf(data.getNendo()));
				pdfWriter.print("納付金コード", data.getPayCd());
				pdfWriter.print("納付金名称", data.getPayName());
				pdfWriter.print("分納区分", data.getBunnoKbnName());
				pdfWriter.print("分割番号", String.valueOf(data.getBunkatsuNo()));

				// 納入期限を設定
				if (data.getPayLimitDate() != null) {
					pdfWriter.print("納入期限", data.getPayLimitDate().toString());
				}

				// 発行日を設定
				if (condition.getCondition() != null && condition.getCondition().getHattyuDate() != null) {
					pdfWriter.print("発行日", condition.getCondition().getHattyuDate().toString());
				}

				// 通信欄を設定
				if (condition.getCondition() != null && condition.getCondition().getTsuukyak() != null) {
					pdfWriter.print("通信欄", condition.getCondition().getTsuukyak());
				}

				// 次のページに移動（最後のデータでない場合）
				pdfWriter.next();

				addNormalCount(1);
				addCount(1);
			}

			logger.info("PDF ファイル生成完了：" + dataList.size() + "件");

		} finally {
			pdfWriter.close();
		}
		
		logger.info("PDF ファイル生成終了");
		return pdfFileDto;
	}

	/**
	 * 納付金通知書データを取得
	 * EXと同様に、選択された納付金に対応する学生情報と金額情報を含む完全なデータを取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 納付金通知書データリスト
	 * @throws Exception 例外
	 */
	private List<Xgm003DTO02> getNotificationData(final DbSession dbs, final Xgm003AsyncDTO condition) throws Exception {
		List<Xgm003DTO02> resultList = new ArrayList<>();

		// 選択された納付金リストが存在する場合
		if (condition.getPayList() != null && !condition.getPayList().isEmpty()) {
			// 各納付金データに対して、学生情報と金額情報を補完
			for (Xgm003DTO02 payData : condition.getPayList()) {
				Xgm003DTO02 enrichedData = enrichPaymentDataWithStudentInfo(dbs, payData, condition);
				resultList.add(enrichedData);
			}
			return resultList;
		}

		// 選択された納付金リストが存在しない場合は、検索条件で取得
		// EXのロジックを参考に、payListとkanriList（gaksekiCdList）の両方を使用してデータを絞り込む

		// gaksekiCdListが空の場合は処理対象なし
		if (condition.getGaksekiCdList() == null || condition.getGaksekiCdList().isEmpty()) {
			logger.warn("対象学籍番号リストが空のため、処理対象データなし");
			return resultList; // 空のリストを返す
		}

		Xgm003ConditionDTO02 searchCondition = new Xgm003ConditionDTO02();

		// 基本的な検索条件を設定
		if (condition.getConditionHeader() != null) {
			// ヘッダ部の基本条件を設定
			searchCondition.setWariateNendo(condition.getConditionHeader().getWariateNendo());
			searchCondition.setGyomucd(condition.getConditionHeader().getGyomucd());
			searchCondition.setNoufuKinShubetsu(condition.getConditionHeader().getNoufuKinShubetsu());
		}

		// EXのロジックを参考に、gaksekiCdListを使用して対象学生を絞り込む
		// 学籍番号リストを検索条件に設定
		searchCondition.setGaksekiCdList(condition.getGaksekiCdList());

		// サービスを使用してデータを取得（学籍番号で絞り込み済み）
		List<Xgm003DTO02> dataList = xgm003Service.getPayHListByGaksekiCdList(searchCondition);

		// データが存在しない場合はエラー
		if (dataList.isEmpty()) {
			throw new GakuenException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, "納付金"));
		}

		// 各データに学生情報を補完
		for (Xgm003DTO02 payData : dawtaList) {
			Xgm003DTO02 enrichedData = enrichPaymentDataWithStudentInfo(dbs, payData, condition);
			resultList.add(enrichedData);
		}

		return resultList;
	}

	/**
	 * CSV データ行を出力
	 * EXと同じ21フィールド形式で出力
	 *
	 * @param csvWriter CSV ライター
	 * @param data 納付金データ
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	private void writeCsvDataLine(final ICollectiveWriter csvWriter, final Xgm003DTO02 data, final Xgm003AsyncDTO condition) throws Exception {
		// EXと同じ21フィールド形式でCSV出力
		List<String> csvLine = new ArrayList<>();

		// 01: データ区分（固定値：2）
		csvLine.add("2");

		// 02: 処理区分（固定値：0）
		csvLine.add("0");

		// 03: 顧客番号（委託人コード）
		csvLine.add(data.getKokyakuNo() != null ? data.getKokyakuNo() : "");

		// 04: 確認番号
		csvLine.add(data.getKakuninNo() != null ? data.getKakuninNo() : "");

		// 05: コンビニネットコード
		csvLine.add(data.getKonbiniNetCd() != null ? data.getKonbiniNetCd() : "");

		// 06: 請求金額
		csvLine.add(data.getSeikyuGaku() != null ? String.valueOf(data.getSeikyuGaku()) : "0");

		// 07: 元金
		csvLine.add(data.getGankin() != null ? String.valueOf(data.getGankin()) : "0");

		// 08: 延滞金額
		csvLine.add(data.getEntaiGaku() != null ? String.valueOf(data.getEntaiGaku()) : "0");

		// 09: 消費税
		csvLine.add(data.getShohizei() != null ? String.valueOf(data.getShohizei()) : "0");

		// 10: 請求内容カナ
		csvLine.add(data.getSeikyuNaiyoKana() != null ? data.getSeikyuNaiyoKana() : "");

		// 11: 請求内容漢字
		csvLine.add(data.getSeikyuNaiyoKanji() != null ? data.getSeikyuNaiyoKanji() : "");

		// 12: 氏名カナ
		csvLine.add(data.getShimeiKana() != null ? data.getShimeiKana() : "");

		// 13: 氏名漢字
		csvLine.add(data.getShimeiKanji() != null ? data.getShimeiKanji() : "");

		// 14-20: 空フィールド（7個）
		for (int i = 0; i < 7; i++) {
			csvLine.add("");
		}

		// 21: 請求情報有効期限
		csvLine.add(data.getSeikyuJohoYukoKigen() != null ?
			data.getSeikyuJohoYukoKigen().toString() : "");

		// CSV行を出力
		csvWriter.writeLineData(csvLine, false);
	}

	/**
	 * 納付金データに学生情報と金額情報を補完
	 * EXのgetDetailOutDataメソッドのロジックを参考に実装
	 *
	 * @param dbs DBセッション
	 * @param payData 納付金データ
	 * @param condition 非同期処理DTO
	 * @return 補完された納付金データ
	 * @throws Exception 例外
	 */
	private Xgm003DTO02 enrichPaymentDataWithStudentInfo(final DbSession dbs, final Xgm003DTO02 payData, final Xgm003AsyncDTO condition) throws Exception {
		// 元のデータをコピー
		Xgm003DTO02 enrichedData = new Xgm003DTO02();
		// 基本的な納付金情報をコピー
		enrichedData.setNendo(payData.getNendo());
		enrichedData.setPayCd(payData.getPayCd());
		enrichedData.setPatternCd(payData.getPatternCd());
		enrichedData.setBunnoKbnCd(payData.getBunnoKbnCd());
		enrichedData.setBunkatsuNo(payData.getBunkatsuNo());
		enrichedData.setPayName(payData.getPayName());
		enrichedData.setPayLimitDate(payData.getPayLimitDate());

		try {
			// EXのロジックを参考に、学生情報と金額情報を取得

			// 1. 学生情報の取得（EXのghUtilStudentName.getStudentName()に相当）
			String gakseiName = "";
			String gakseiNameKana = "";
			if (payData.getGaksekiCd() != null && !payData.getGaksekiCd().isEmpty()) {
				// 学生氏名取得共通処理を使用
				PkbGakNameLogic pkbGakNameLogic = new PkbGakNameLogic(dbs, new Date());
				gakseiName = pkbGakNameLogic.findPkGakNameByGaksekiCd(payData.getGaksekiCd(), null, null, true);

				// 学生の詳細情報を取得（氏名カナなど）
				PkbGakHatbnDAO pkbGakHatbnDAO = dbs.getDao(PkbGakHatbnDAO.class);
				PkbGakHatbnAR pkbGakHatbnAR = pkbGakHatbnDAO.findByPrimaryKey(payData.getGaksekiCd());
				if (pkbGakHatbnAR != null) {
					PkbGakDAO pkbGakDAO = dbs.getDao(PkbGakDAO.class);
					PkbGakAR pkbGakAR = pkbGakDAO.findByPrimaryKey(pkbGakHatbnAR.getKanriNo());
					if (pkbGakAR != null) {
						gakseiNameKana = pkbGakAR.getGakseiNameKana();
					}
				}
			}

			// 2. 金額情報の計算（EXのGhgPaywItemDAO.findByPrimaryKey6()に相当）
			// TODO: 新系統の納付金明細取得サービスを使用して実際の金額を計算
			// 暫定実装：基本金額を設定
			Long seikyuGaku = 0L; // 請求金額
			Long gankin = 0L; // 元金
			Long entaiGaku = 0L; // 延滞金額
			Long shohizei = 0L; // 消費税

			// 3. 確認番号の生成（EXのcreateCheakNo()に相当）
			String kakuninNo = generateKakuninNo(payData);
			enrichedData.setKakuninNo(kakuninNo);

			// 4. 顧客番号の設定（EXの委託人コードに相当）
			// TODO: 設定ファイルまたはマスタから取得
			enrichedData.setKokyakuNo("");

			// 5. コンビニネットコードの生成（EXのcreateBcd()に相当）
			String konbiniNetCd = generateKonbiniNetCd(payData);
			enrichedData.setKonbiniNetCd(konbiniNetCd);

			// 6. 請求内容の設定
			enrichedData.setSeikyuNaiyoKana(payData.getPayName()); // 納付金名称をカナとして使用
			enrichedData.setSeikyuNaiyoKanji(payData.getPayName()); // 納付金名称を漢字として使用

			// 7. 金額情報の設定
			enrichedData.setSeikyuGaku(seikyuGaku);
			enrichedData.setGankin(gankin);
			enrichedData.setEntaiGaku(entaiGaku);
			enrichedData.setShohizei(shohizei);

			// 8. 学生氏名の設定
			enrichedData.setShimeiKana(gakseiNameKana != null ? gakseiNameKana : "");
			enrichedData.setShimeiKanji(gakseiName != null ? gakseiName : "");

			// 9. 有効期限の設定
			// EXのロジックを参考に有効期限を計算（暫定実装）
			enrichedData.setSeikyuJohoYukoKigen(payData.getPayLimitDate());

		} catch (Exception e) {
			logger.warn("学生情報補完エラー: " + e.getMessage());
			// エラーが発生した場合は基本情報のみ設定
			setDefaultCsvValues(enrichedData);
		}

		return enrichedData;
	}

	/**
	 * 確認番号を生成
	 * EXのcreateCheakNoメソッドのロジックを参考
	 *
	 * @param payData 納付金データ
	 * @return 確認番号
	 */
	private String generateKakuninNo(final Xgm003DTO02 payData) {
		try {
			// EXのロジック：chkNo="1"+ BizXrmComInfo.getFriGyoumuNo(getDbs(),paycd);
			// 新系統では暫定実装として、固定値+納付金コードを使用
			String baseNo = "1" + payData.getPayCd();

			// EXでは年度別に最大値を取得していたが、新系統では暫定実装
			// TODO: 実際の実装では、データベースから最大値を取得して連番を生成
			String sequenceNo = String.format("%04d",
				Math.abs((payData.getNendo() + payData.getPayCd().hashCode() +
				payData.getBunnoKbnCd() + payData.getBunkatsuNo()) % 10000));

			String kakuninNo = baseNo + sequenceNo;

			// 最大桁数制限
			if (kakuninNo.length() > 20) {
				kakuninNo = kakuninNo.substring(0, 20);
			}

			return kakuninNo;
		} catch (Exception e) {
			logger.warn("確認番号生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * コンビニネットコードを生成
	 * EXのcreateBcdメソッドのロジックを参考
	 *
	 * @param payData 納付金データ
	 * @return コンビニネットコード
	 */
	private String generateKonbiniNetCd(final Xgm003DTO02 payData) {
		try {
			// EXのロジックを参考にバーコードを生成
			// EXでは企業コード+委託コード+顧客番号+期限+金額チェック+請求番号で構成
			StringBuilder bcd = new StringBuilder();

			// 企業コード（暫定値）
			bcd.append("91234"); // TODO: 実際の企業コードを設定ファイルから取得

			// 委託コード（暫定値）
			bcd.append("567"); // TODO: 実際の委託コードを設定

			// 顧客番号（確認番号の一部を使用）
			String kakuninNo = generateKakuninNo(payData);
			if (kakuninNo.length() >= 8) {
				bcd.append(kakuninNo.substring(0, 8));
			} else {
				bcd.append(String.format("%-8s", kakuninNo).replace(' ', '0'));
			}

			// 期限（YYYYMMDD形式）
			if (payData.getPayLimitDate() != null) {
				String limitDate = payData.getPayLimitDate().toString().replace("-", "");
				bcd.append(limitDate);
			} else {
				bcd.append("00000000");
			}

			// 金額（暫定値）
			bcd.append(String.format("%08d", payData.getSeikyuGaku() != null ? payData.getSeikyuGaku() : 0));

			return bcd.toString();
		} catch (Exception e) {
			logger.warn("コンビニネットコード生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * CSV出力用のデフォルト値を設定
	 *
	 * @param data 納付金データ
	 */
	private void setDefaultCsvValues(final Xgm003DTO02 data) {
		// エラー時のデフォルト値を設定
		if (data.getKokyakuNo() == null) data.setKokyakuNo("");
		if (data.getKakuninNo() == null) data.setKakuninNo("");
		if (data.getKonbiniNetCd() == null) data.setKonbiniNetCd("");
		if (data.getSeikyuGaku() == null) data.setSeikyuGaku(0L);
		if (data.getGankin() == null) data.setGankin(0L);
		if (data.getEntaiGaku() == null) data.setEntaiGaku(0L);
		if (data.getShohizei() == null) data.setShohizei(0L);
		if (data.getSeikyuNaiyoKana() == null) data.setSeikyuNaiyoKana("");
		if (data.getSeikyuNaiyoKanji() == null) data.setSeikyuNaiyoKanji("");
		if (data.getShimeiKana() == null) data.setShimeiKana("");
		if (data.getShimeiKanji() == null) data.setShimeiKanji("");
	}

}
