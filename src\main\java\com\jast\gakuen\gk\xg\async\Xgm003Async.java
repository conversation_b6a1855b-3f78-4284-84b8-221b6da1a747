/*
 * Xgm003Async.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.async;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.UserInfo;
import com.jast.gakuen.core.common.annotation.RxAsyncErrorCheck;
import com.jast.gakuen.core.common.annotation.RxAsyncExecute;
import com.jast.gakuen.core.common.annotation.RxAsyncInit;
import com.jast.gakuen.core.common.annotation.RxAsyncNumberCount;
import com.jast.gakuen.core.gk.async.GkBaseCollectiveOutputAsync;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.dto.FileDTO;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.RxLogger;
import com.jast.gakuen.core.common.async.ICollectiveWriter;
import com.jast.gakuen.core.gk.util.UtilFormWriter;
import com.jast.gakuen.gk.xg.dto.Xgm003CollectiveOutputDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;
import com.jast.gakuen.core.common.exception.GakuenException;

/**
 * 学生納付金通知書出力（CSV+PDF一括ダウンロード）非同期処理クラス
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class Xgm003Async extends GkBaseCollectiveOutputAsync {

	/**
	 * プロダクトコード
	 */
	public static final String PRD_CD = "Xg";

	/**
	 * 非同期処理ID
	 */
	public static final String ASYNC_EXEC_ID = "Xgm003Async";

	/**
	 * 処理名：CSV+PDF一括ダウンロード
	 */
	public static final String EXEC_PACKAGE_OUTPUT = "EXEC_PACKAGE_OUTPUT";

	/**
	 * ログ出力
	 */
	protected RxLogger LOGGER;

	/**
	 * ログインユーザ情報
	 */
	protected UserInfo userInfo;

	/**
	 * 学生納付金通知書サービス
	 */
	@Inject
	private IXgm003Service xgm003Service;

	/**
	 * コンストラクタ
	 *
	 * @throws Exception 例外
	 */
	public Xgm003Async() throws Exception {
		super();
		LOGGER = new RxLogger(Xgm003Async.class);
	}

	/**
	 * 初期処理
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 */
	@RxAsyncInit(EXEC_PACKAGE_OUTPUT)
	public void init(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) {
		// ログインユーザ情報を取得
		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		this.userInfo = sessionInfo.getLoginUser();
	}

	/**
	 * エラーチェック
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return エラーチェック結果
	 * @throws Exception 例外
	 */
	@RxAsyncErrorCheck(EXEC_PACKAGE_OUTPUT)
	public List<Message> errorCheck(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		List<Message> messageList = new ArrayList<>();

		if (condition.getPayList().isEmpty()) {
			// E_SY_00058={0}は存在しません。
			messageList.add(new Message("SY", Message.TypeCode.E, 58, "納付金"));
		}

		return messageList;
	}

	/**
	 * 処理件数取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 処理件数
	 * @throws Exception 例外
	 */
	@RxAsyncNumberCount(EXEC_PACKAGE_OUTPUT)
	public int getCount(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 処理件数は固定で2（CSV + PDF）
		return 2;
	}

	/**
	 * 業務処理：CSV+PDF一括ダウンロード
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncExecute(EXEC_PACKAGE_OUTPUT)
	public void executePackageOutput(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {

		LOGGER.info("CSV+PDF一括ダウンロード処理開始");

		// 圧縮対象ファイルのリスト
		List<FileDTO> zipFileDtoList = new ArrayList<>();

		try {
			// 1. CSV ファイル生成
			FileDTO csvFileDto = generateCsvFile(dbs, condition);
			if (csvFileDto != null) {
				zipFileDtoList.add(csvFileDto);
				addNormalCount(1);
				addCount(1);
			}

			// 2. PDF ファイル生成
			FileDTO pdfFileDto = generatePdfFile(dbs, condition);
			if (pdfFileDto != null) {
				zipFileDtoList.add(pdfFileDto);
				addNormalCount(1);
				addCount(1);
			}

			// 3. ZIP ファイル生成
			if (!zipFileDtoList.isEmpty()) {
				FileDTO zipFileDTO = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_ZIP01", FileTypeConst.ZIP);
				// ZIPファイルを生成し、圧縮対象ファイルを登録する
				compressOutputFile(zipFileDTO, zipFileDtoList, true);
			}

		} catch (Exception e) {
			LOGGER.error(e);
			addErrorCount(1);
			throw e;
		}

		LOGGER.info("CSV+PDF一括ダウンロード処理終了");
	}

	/**
	 * CSV ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return CSV ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generateCsvFile(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		LOGGER.info("CSV ファイル生成開始");

		// CSV ファイルのFileDTOを取得
		FileDTO csvFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_CSV01", FileTypeConst.CSV);

		// 出力ファイル用の一括出力用ユーティリティを取得する
		try (ICollectiveWriter csvWriter = getCollectiveWriter(csvFileDto)) {

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// データ行を出力
			for (Xgm003DTO02 data : dataList) {
				writeCsvDataLine(csvWriter, data, condition);
				addNormalCount(1);
				addCount(1);
			}

			LOGGER.info("CSV ファイル生成完了：" + dataList.size() + "件");
		}

		LOGGER.info("CSV ファイル生成終了");
		return csvFileDto;
	}

	/**
	 * PDF ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return PDF ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generatePdfFile(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		LOGGER.info("PDF ファイル生成開始");

		// PDF ファイルのFileDTOを取得
		FileDTO pdfFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_PDF01", FileTypeConst.PDF);

		// SVF を使用してPDF生成
		UtilFormWriter pdfWriter = new UtilFormWriter();

		try {
			// PDF ファイルを開く
			pdfWriter.open(UtilFormWriter.DocFileType.PDF, pdfFileDto.getAbsolutePath());

			// SVF テンプレート（Xgm003RPT01.xml）を使用してPDF生成
			pdfWriter.setForm(com.jast.gakuen.core.common.constant.code.PrdKbn.XG, "Xgm003RPT01");

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// 各納付金データに対してPDFページを生成
			for (Xgm003DTO02 data : dataList) {
				// 基本情報を設定
				pdfWriter.print("年度", String.valueOf(data.getNendo()));
				pdfWriter.print("納付金コード", data.getPayCd());
				pdfWriter.print("納付金名称", data.getPayName());
				pdfWriter.print("分納区分", data.getBunnoKbnName());
				pdfWriter.print("分割番号", String.valueOf(data.getBunkatsuNo()));

				// 納入期限を設定
				if (data.getPayLimitDate() != null) {
					pdfWriter.print("納入期限", data.getPayLimitDate().toString());
				}

				// 発行日を設定
				if (condition.getHattyuDate() != null) {
					pdfWriter.print("発行日", condition.getHattyuDate().toString());
				}

				// 通信欄を設定
				if (condition.getTsuukyak() != null) {
					pdfWriter.print("通信欄", condition.getTsuukyak());
				}

				// 次のページに移動（最後のデータでない場合）
				pdfWriter.next();

				addNormalCount(1);
				addCount(1);
			}

			LOGGER.info("PDF ファイル生成完了：" + dataList.size() + "件");

		} finally {
			pdfWriter.close();
		}

		LOGGER.info("PDF ファイル生成終了");
		return pdfFileDto;
	}

	/**
	 * 納付金通知書データを取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 納付金通知書データリスト
	 * @throws Exception 例外
	 */
	private List<Xgm003DTO02> getNotificationData(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		List<Xgm003DTO02> resultList = new ArrayList<>();

		// 選択された納付金リストが存在する場合
		if (condition.getPayList() != null && !condition.getPayList().isEmpty()) {
			// 各納付金データに対して、学生情報と金額情報を補完
			for (Xgm003DTO02 payData : condition.getPayList()) {
				Xgm003DTO02 enrichedData = enrichPaymentDataWithStudentInfo(dbs, payData, condition);
				resultList.add(enrichedData);
			}
			return resultList;
		}

		// 選択された納付金リストが存在しない場合は、検索条件で取得
		// 学籍番号リストが空の場合は処理対象なし
		if (condition.getGaksekiCdList() == null || condition.getGaksekiCdList().isEmpty()) {
			LOGGER.warn("対象学籍番号リストが空のため、処理対象データなし");
			return resultList; // 空のリストを返す
		}

		Xgm003ConditionDTO02 searchCondition = new Xgm003ConditionDTO02();

		// 基本的な検索条件を設定
		searchCondition.setWariateNendo(condition.getWariateNendo());
		searchCondition.setGyomucd(condition.getGyomucd());
		searchCondition.setNoufuKinShubetsu(condition.getNoufuKinShubetsu());

		// 学籍番号リストを検索条件に設定
		searchCondition.setGaksekiCdList(condition.getGaksekiCdList());

		// サービスを使用してデータを取得（学籍番号で絞り込み済み）
		List<Xgm003DTO02> dataList = xgm003Service.getPayHListByGaksekiCdList(searchCondition);

		// データが存在しない場合はエラー
		if (dataList.isEmpty()) {
			throw new GakuenException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, "納付金"));
		}

		// 各データに学生情報を補完
		for (Xgm003DTO02 payData : dataList) {
			Xgm003DTO02 enrichedData = enrichPaymentDataWithStudentInfo(dbs, payData, condition);
			resultList.add(enrichedData);
		}

		return resultList;
	}

	/**
	 * CSV データ行を出力
	 *
	 * @param csvWriter CSV ライター
	 * @param data 納付金データ
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	private void writeCsvDataLine(final ICollectiveWriter csvWriter, final Xgm003DTO02 data, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 21フィールド形式でCSV出力
		List<String> csvLine = new ArrayList<>();

		// 01: データ区分（固定値：2）
		csvLine.add("2");

		// 02: 処理区分（固定値：0）
		csvLine.add("0");

		// 03: 顧客番号（委託人コード）
		csvLine.add("");

		// 04: 確認番号
		csvLine.add(generateKakuninNo(data));

		// 05: コンビニネットコード
		csvLine.add(generateKonbiniNetCd(data));

		// 06: 請求金額
		csvLine.add("0");

		// 07: 元金
		csvLine.add("0");

		// 08: 延滞金額
		csvLine.add("0");

		// 09: 消費税
		csvLine.add("0");

		// 10: 請求内容カナ
		csvLine.add(data.getPayName() != null ? data.getPayName() : "");

		// 11: 請求内容漢字
		csvLine.add(data.getPayName() != null ? data.getPayName() : "");

		// 12: 氏名カナ
		csvLine.add("");

		// 13: 氏名漢字
		csvLine.add("");

		// 14-20: 空フィールド（7個）
		for (int i = 0; i < 7; i++) {
			csvLine.add("");
		}

		// 21: 請求情報有効期限
		csvLine.add(data.getPayLimitDate() != null ? data.getPayLimitDate().toString() : "");

		// CSV行を出力
		csvWriter.writeLineData(csvLine, false);
	}

	/**
	 * 納付金データに学生情報と金額情報を補完
	 *
	 * @param dbs DBセッション
	 * @param payData 納付金データ
	 * @param condition 非同期処理DTO
	 * @return 補完された納付金データ
	 * @throws Exception 例外
	 */
	private Xgm003DTO02 enrichPaymentDataWithStudentInfo(final DbSession dbs, final Xgm003DTO02 payData, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 元のデータをそのまま返す（基本情報は既に設定済み）
		return payData;
	}

	/**
	 * 確認番号を生成
	 *
	 * @param payData 納付金データ
	 * @return 確認番号
	 */
	private String generateKakuninNo(final Xgm003DTO02 payData) {
		try {
			// 暫定実装として、固定値+納付金コードを使用
			String baseNo = "1" + payData.getPayCd();

			// 年度と納付金コードから連番を生成
			String sequenceNo = String.format("%04d",
				Math.abs((payData.getNendo() + payData.getPayCd().hashCode() +
				payData.getBunnoKbnCd() + payData.getBunkatsuNo()) % 10000));

			String kakuninNo = baseNo + sequenceNo;

			// 最大桁数制限
			if (kakuninNo.length() > 20) {
				kakuninNo = kakuninNo.substring(0, 20);
			}

			return kakuninNo;
		} catch (Exception e) {
			LOGGER.warn("確認番号生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * コンビニネットコードを生成
	 *
	 * @param payData 納付金データ
	 * @return コンビニネットコード
	 */
	private String generateKonbiniNetCd(final Xgm003DTO02 payData) {
		try {
			// バーコードを生成
			StringBuilder bcd = new StringBuilder();

			// 企業コード（暫定値）
			bcd.append("91234");

			// 委託コード（暫定値）
			bcd.append("567");

			// 顧客番号（確認番号の一部を使用）
			String kakuninNo = generateKakuninNo(payData);
			if (kakuninNo.length() >= 8) {
				bcd.append(kakuninNo.substring(0, 8));
			} else {
				bcd.append(String.format("%-8s", kakuninNo).replace(' ', '0'));
			}

			// 期限（YYYYMMDD形式）
			if (payData.getPayLimitDate() != null) {
				String limitDate = payData.getPayLimitDate().toString().replace("-", "");
				bcd.append(limitDate);
			} else {
				bcd.append("00000000");
			}

			// 金額（暫定値）
			bcd.append(String.format("%08d", 0));

			return bcd.toString();
		} catch (Exception e) {
			LOGGER.warn("コンビニネットコード生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

}
