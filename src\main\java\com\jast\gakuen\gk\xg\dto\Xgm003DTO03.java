/*
 * Xgm003DTO03.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import com.jast.gakuen.core.common.annotation.RxOpt;
import com.jast.gakuen.core.common.dto.OrderItemDTO;
import com.jast.gakuen.core.gk.dto.GkOrdDesignationDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 並び順指定内容
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003DTO03 extends GkOrdDesignationDTO {

	/**
	 * 並び順指定 ※同一画面の別タブに配置した並び順指定をオプションに別IDで登録するために必要
	 */
	@RxOpt(value = "t2_orderItems")
	protected List<OrderItemDTO> orderItems = new ArrayList<>();

}
